name: Deploy Backend Lambda Function

on:
  push:
    branches:
      - development
    paths:
      - Backend/**
      - .github/workflows/Backend-lambda-function.yml
  workflow_dispatch:  # Allow manual trigger from GitHub UI

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    # Install Node.js dependencies for Lambda function
    - name: Install dependencies
      run: |
        cd Backend
        npm install
    
    # Configure AWS credentials for ap-south-1 region
    - name: Set up AWS CLI
      uses: aws-actions/configure-aws-credentials@v3
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ap-south-1

    # Zip the Lambda function code
    - name: Zip Lambda function
      run: |
        cd Backend
        zip -r ../my_lambda_function.zip .

    # Deploy the zipped code to AWS Lambda
    - name: Deploy to AWS Lambda
      run: |
        aws lambda update-function-code \
          --function-name mtl_site_function \
          --zip-file fileb://my_lambda_function.zip