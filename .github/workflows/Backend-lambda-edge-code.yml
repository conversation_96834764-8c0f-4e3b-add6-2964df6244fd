name: Deploy <PERSON>@Edge and Update CloudFront

on:
  push:
    branches:
      - development
    paths:
      - terraform/lambda_edge_function/index.js
      - .github/workflows/Backend-lambda-edge-code.yml
    
  workflow_dispatch:  # Allow manual trigger from GitHub UI

jobs:
  deploy_lambda_edge:
    runs-on: ubuntu-latest

    env:
      FUNCTION_NAME: mtl-edge-redirect # Lambda@Edge Function Name
      CF_DISTRIBUTION_ID: E3SOMXMO6ZAER7 # CloudFront Distribution ID
      AWS_ACCOUNT_ID: ************ # AWS Account ID

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    # Configure AWS CLI credentials for us-east-1 region
    - name: Set up AWS credentials for us-east-1
      uses: aws-actions/configure-aws-credentials@v3
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: us-east-1

    # Zip the Lambda@Edge function code
    - name: Zip the Lambda@Edge Function
      run: |
        zip lambda_edge_code.zip terraform/lambda_edge_function/index.js -j
    
    # Deploy and publish new Lambda@Edge version, capture published version number
    - name: Deploy and Publish Lambda@Edge Function
      id: publish_lambda 
      run: |        
        response=$(aws lambda update-function-code \
          --function-name $FUNCTION_NAME \
          --zip-file fileb://lambda_edge_code.zip \
          --publish)
        
        version=$(echo "$response" | jq -r '.Version')
        echo "Lambda@Edge function published with version: $version"
        echo "version=$version" >> $GITHUB_OUTPUT

    # Get current CloudFront distribution config
    - name: Fetch current CloudFront config
      run: |
        aws cloudfront get-distribution-config --id $CF_DISTRIBUTION_ID > dist_config.json
        jq '.DistributionConfig' dist_config.json > config.json
        echo "etag=$(jq -r '.ETag' dist_config.json)" >> $GITHUB_ENV

    # Update Lambda@Edge version in CloudFront config
    - name: Update Lambda@Edge version in config
      run: |
        # Fully-qualified new Lambda ARN
        NEW_ARN="arn:aws:lambda:us-east-1:${{ env.AWS_ACCOUNT_ID }}:function:${{ env.FUNCTION_NAME }}:${{ steps.publish_lambda.outputs.version }}"

        echo "Using new Lambda ARN: $NEW_ARN"

        # Replace the first LambdaFunctionAssociation (viewer-request) ARN
        jq --arg arn "$NEW_ARN" '
          .DefaultCacheBehavior.LambdaFunctionAssociations.Items[0].LambdaFunctionARN = $arn
        ' config.json > updated_config.json

    # Deploy updated CloudFront distribution with new Lambda version
    - name: Deploy updated CloudFront distribution
      run: |
        aws cloudfront update-distribution \
          --id $CF_DISTRIBUTION_ID \
          --distribution-config file://updated_config.json \
          --if-match ${{ env.etag }}

    # Invalidate CloudFront cache
    - name: Invalidate CloudFront cache
      run: |
        aws cloudfront create-invalidation \
          --distribution-id $CF_DISTRIBUTION_ID \
          --paths "/*"

# arn:aws:lambda:us-east-1:************:function:mtl-edge-redirect:28